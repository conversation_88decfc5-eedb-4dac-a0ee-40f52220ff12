import express from 'express';
import request from 'supertest';
import chatRoutes from '../chat';

// Create test app
const createTestApp = () => {
  const app = express();
  app.use(express.json());
  app.use('/api/chat', chatRoutes);
  return app;
};

describe('Chat Routes', () => {
  let app: express.Application;

  beforeEach(() => {
    app = createTestApp();
  });

  describe('POST /session', () => {
    it('should create a new chat session', async () => {
      const response = await request(app).post('/api/chat/session').expect(200);

      expect(response.body).toHaveProperty('sessionId');
      expect(response.body.sessionId).toBeDefined();
      expect(typeof response.body.sessionId).toBe('string');
      expect(response.body.sessionId.length).toBeGreaterThan(0);
    });

    it('should create unique session IDs', async () => {
      const response1 = await request(app)
        .post('/api/chat/session')
        .expect(200);

      const response2 = await request(app)
        .post('/api/chat/session')
        .expect(200);

      expect(response1.body.sessionId).not.toBe(response2.body.sessionId);
    });
  });

  describe('POST /message', () => {
    let sessionId: string;

    beforeEach(async () => {
      // Create a session for testing
      const sessionResponse = await request(app).post('/api/chat/session');
      sessionId = sessionResponse.body.sessionId;
    });

    it('should handle basic text messages', async () => {
      const message = {
        sessionId,
        message: 'Hello, how are you?',
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('sessionId');
      expect(response.body.sessionId).toBe(sessionId);
      expect(response.body.message).toBeDefined();
      expect(typeof response.body.message).toBe('string');
      expect(response.body.message.length).toBeGreaterThan(0);
    }, 15000);

    it('should handle halal-related messages with knowledge integration', async () => {
      const message = {
        sessionId,
        message: 'Is beef halal in Islam?',
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.sessionId).toBe(sessionId);
      expect(response.body.message).toBeDefined();
      expect(response.body.message.toLowerCase()).toMatch(/halal|islam|beef/);
    }, 20000);

    it('should handle non-halal messages appropriately', async () => {
      const message = {
        sessionId,
        message: "What's the capital of France?",
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.sessionId).toBe(sessionId);
      expect(response.body.message).toBeDefined();
      expect(response.body.message.toLowerCase()).toMatch(/paris|france/);
    }, 15000);

    it('should validate required fields', async () => {
      const invalidMessage = {
        // Missing sessionId and message
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(invalidMessage)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle empty messages', async () => {
      const message = {
        sessionId,
        message: '',
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle invalid session ID', async () => {
      const message = {
        sessionId: 'invalid-session-id',
        message: 'Hello',
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message);

      // Should either work (creating new session) or return appropriate error
      expect(response.body).toBeDefined();
    });

    it('should maintain conversation context', async () => {
      // Send first message
      const message1 = {
        sessionId,
        message: 'My name is John',
        model: 'gpt-4o-mini',
      };

      await request(app).post('/api/chat/message').send(message1).expect(200);

      // Send follow-up message
      const message2 = {
        sessionId,
        message: 'What is my name?',
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(message2)
        .expect(200);

      expect(response.body.message.toLowerCase()).toMatch(/john/);
    }, 25000);

    it('should handle different models', async () => {
      const models = ['gpt-4o-mini', 'gpt-4o'];

      for (const model of models) {
        const message = {
          sessionId,
          message: `Hello from ${model}`,
          model,
        };

        const response = await request(app)
          .post('/api/chat/message')
          .send(message);

        // Should work with different models or return appropriate error
        expect(response.body).toBeDefined();
      }
    }, 20000);

    it('should handle concurrent messages in same session', async () => {
      const messages = [
        'What is halal food?',
        'Tell me about Islamic prayer',
        'What are the five pillars of Islam?',
      ].map((msg) => ({
        sessionId,
        message: msg,
        model: 'gpt-4o-mini',
      }));

      const promises = messages.map((message) =>
        request(app).post('/api/chat/message').send(message),
      );

      const responses = await Promise.all(promises);

      responses.forEach((response) => {
        expect(response.status).toBe(200);
        expect(response.body.message).toBeDefined();
        expect(response.body.sessionId).toBe(sessionId);
      });
    }, 30000);
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/chat/message')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle very long messages', async () => {
      const sessionResponse = await request(app).post('/api/chat/session');
      const sessionId = sessionResponse.body.sessionId;

      const longMessage = {
        sessionId,
        message: 'A'.repeat(10000), // Very long message
        model: 'gpt-4o-mini',
      };

      const response = await request(app)
        .post('/api/chat/message')
        .send(longMessage);

      // Should handle gracefully, either process or reject with appropriate error
      expect(response.body).toBeDefined();
    });
  });
});
