import { Hono } from 'hono';
import agentAuthService from '../services/agentAuth';
import databaseService from '../services/database';
import type {
  AgentDashboardStats,
  AgentMessageRequest,
  AgentMessageResponse,
  ErrorResponse,
  HandoverRequest,
  SessionHandoverRequest,
  SessionHandoverResponse,
} from '../types';

const router = new Hono();

// Get agent dashboard statistics
router.get('/stats', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    // Get actual stats from database
    const stats = await databaseService.getSessionStats();

    return c.json(stats);
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get pending handover requests
router.get('/handovers/pending', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const requests = await databaseService.getPendingHandoverRequests();
    return c.json(requests);
  } catch (error) {
    console.error('Get pending handovers error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Create handover request
router.post(
  '/handover',
  async (req: Request, res: Response<SessionHandoverResponse>) => {
    try {
      const { sessionId, reason, priority }: SessionHandoverRequest = req.body;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID is required',
        });
      }

      const handoverRequestId = await databaseService.createHandoverRequest(
        sessionId,
        'user',
        reason,
        priority || 'normal',
      );

      res.json({
        success: true,
        handoverRequestId,
      });
    } catch (error) {
      console.error('Create handover request error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  },
);

// Assign handover request to agent
router.post(
  '/handovers/:requestId/assign',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const requestId = Number.parseInt(req.params.requestId);

      if (Number.isNaN(requestId)) {
        return res.status(400).json({ error: 'Invalid request ID' });
      }

      await databaseService.assignHandoverRequest(requestId, req.agent.agentId);

      // Also assign the session to the agent
      const requests = await databaseService.getPendingHandoverRequests();
      const request = requests.find((r) => r.id === requestId);

      if (request) {
        await databaseService.assignSessionToAgent(
          request.sessionId,
          req.agent.agentId,
        );
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Assign handover request error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

// Get agent's active sessions
router.get(
  '/active',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const sessions = await databaseService.getAgentActiveSessions(
        req.agent.agentId,
      );
      res.json(sessions);
    } catch (error) {
      console.error('Get active sessions error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

// Get all active sessions (for agent takeover)
router.get(
  '/all-active',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const sessions = await databaseService.getAllActiveSessions();
      res.json(sessions);
    } catch (error) {
      console.error('Get all active sessions error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

// Get specific session details
router.get(
  '/:sessionId',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      const { sessionId } = req.params;
      const session = await databaseService.getChatSession(sessionId);

      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      res.json(session);
    } catch (error) {
      console.error('Get session details error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

// Take over a session (assign to agent)
router.post(
  '/:sessionId/takeover',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const { sessionId } = req.params;

      // Check if session exists and is active
      const session = await databaseService.getChatSession(sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      if (session.status !== 'active') {
        return res.status(400).json({ error: 'Session is not active' });
      }

      // Check if session is already assigned to an agent
      const existingAssignment = await databaseService.getAgentActiveSessions(
        req.agent.agentId,
      );
      const isAlreadyAssigned = existingAssignment.some(
        (s) => s.id === sessionId,
      );

      if (isAlreadyAssigned) {
        return res
          .status(400)
          .json({ error: 'Session is already assigned to you' });
      }

      // Create session assignment (this also marks session as handed over)
      await databaseService.assignSessionToAgent(sessionId, req.agent.agentId);

      res.json({ success: true, message: 'Session taken over successfully' });
    } catch (error) {
      console.error('Take over session error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

// Send message as agent
router.post(
  '/:sessionId/message',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response<AgentMessageResponse>) => {
    try {
      if (!req.agent) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { sessionId } = req.params;
      const {
        content,
        type,
        fileUrl,
        fileName,
        audioUrl,
      }: AgentMessageRequest = req.body;

      if (!content) {
        return res.status(400).json({
          success: false,
          error: 'Message content is required',
        });
      }

      const messageId = await databaseService.addChatMessage(
        sessionId,
        'agent',
        content,
        req.agent.agentId,
        undefined, // imageUrl
        audioUrl,
        fileUrl,
        fileName,
      );

      res.json({
        success: true,
        messageId,
      });
    } catch (error) {
      console.error('Send agent message error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  },
);

// Complete session (return to bot)
router.post(
  '/:sessionId/complete',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const { sessionId } = req.params;

      await databaseService.completeSessionAssignment(sessionId);

      res.json({ success: true });
    } catch (error) {
      console.error('Complete session error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
);

export default router;
